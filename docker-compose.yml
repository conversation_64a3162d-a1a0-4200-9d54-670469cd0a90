version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hia-suag-ma-app
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: "********************************************/hiasangma?schema=public"
      
      # Socket.IO
      SOCKET_IO_PATH: "/api/socket/io"
      
      # JWT Configuration
      JWT_SECRET: "your-jwt-secret-change-in-production"
      JWT_EXPIRES_IN: "24h"
      
      # AWS S3 Configuration (replace with your actual values)
      AWS_ACCESS_KEY_ID: "your-aws-access-key"
      AWS_SECRET_ACCESS_KEY: "your-aws-secret-key"
      AWS_REGION: "ap-southeast-1"
      AWS_S3_BUCKET_NAME: "your-bucket-name"
      
      # CDN URL (replace with your actual CDN URL)
      NEXT_PUBLIC_AWS_CDN_URL: "https://your-cdn-url.cloudfront.net"
      
      # LLM API Configuration (replace with your actual values)
      NEXT_PUBLIC_LITE_LLM_API_KEY: "your-llm-api-key"
      NEXT_PUBLIC_LITE_LLM_ENDPOINT: "https://your-llm-endpoint.com"
      
      # Node Environment
      NODE_ENV: "production"
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    networks:
      - app-network
    volumes:
      - ./public/uploads:/app/public/uploads

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
