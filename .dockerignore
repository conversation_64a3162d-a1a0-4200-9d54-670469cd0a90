# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.tsbuildinfo

# Debug
*.log

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# IDE
.vscode
.idea

# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Documentation
README.md

# Testing
coverage/
.nyc_output

# Temporary files
tmp/
temp/
