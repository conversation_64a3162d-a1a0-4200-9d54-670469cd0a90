# Docker Setup for Hia Suag Ma

This project includes Docker configuration for both development and production environments.

## Files Created

- `Dockerfile` - Production Docker configuration
- `Dockerfile.dev` - Development Docker configuration
- `docker-compose.yml` - Production Docker Compose setup
- `docker-compose.dev.yml` - Development Docker Compose setup
- `.dockerignore` - Files to exclude from Docker build
- `.env.example` - Example environment variables

## Prerequisites

- Docker and Docker Compose installed on your system
- Copy `.env.example` to `.env` and update with your actual values

## Environment Variables Setup

**Important**: The `.env` file is not copied into the Docker container for security reasons. You need to:

1. Copy the example file:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your actual values:
   - Database credentials
   - AWS S3 credentials
   - JWT secret
   - LLM API credentials

3. For production, update the environment variables in `docker-compose.yml`
4. For development, update the environment variables in `docker-compose.dev.yml`

## Development Environment

To run the development environment:

```bash
# Build and start development containers
docker-compose -f docker-compose.dev.yml up --build

# Or run in detached mode
docker-compose -f docker-compose.dev.yml up -d --build

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop containers
docker-compose -f docker-compose.dev.yml down
```

The development server will be available at: http://localhost:3001

## Production Environment

To run the production environment:

```bash
# Build and start production containers
docker-compose up --build

# Or run in detached mode
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop containers
docker-compose down
```

The production server will be available at: http://localhost:3000

## Database

Both environments include PostgreSQL containers:
- Development: Port 5433
- Production: Port 5432

## Volumes

- PostgreSQL data is persisted in Docker volumes
- Upload files are mounted to `./public/uploads`
- Development environment mounts the entire source code for hot reloading

## Useful Commands

```bash
# View running containers
docker ps

# Execute commands in running container
docker exec -it hia-suag-ma-app-dev sh

# View container logs
docker logs hia-suag-ma-app

# Rebuild containers
docker-compose build --no-cache

# Remove all containers and volumes
docker-compose down -v

# Run Prisma commands in container
docker exec -it hia-suag-ma-app npx prisma migrate dev
docker exec -it hia-suag-ma-app npx prisma studio
```

## Security Notes

- Never commit your actual `.env` file to version control
- Update default passwords and secrets in production
- Use proper secrets management in production environments
- The `.env` file is excluded from Docker builds via `.dockerignore`

## Troubleshooting

1. **Port conflicts**: Change ports in docker-compose files if needed
2. **Permission issues**: Ensure Docker has proper permissions
3. **Build failures**: Clear Docker cache with `docker system prune`
4. **Database connection**: Ensure containers are on the same network
